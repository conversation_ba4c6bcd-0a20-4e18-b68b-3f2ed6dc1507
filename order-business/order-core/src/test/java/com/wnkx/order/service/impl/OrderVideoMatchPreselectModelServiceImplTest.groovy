package com.wnkx.order.service.impl

import cn.hutool.core.date.DateUtil
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper
import com.ruoyi.common.core.enums.PreselectModelAddTypeEnum
import com.ruoyi.common.core.enums.PreselectModelOustTypeEnum
import com.ruoyi.common.core.enums.PreselectStatusEnum
import com.ruoyi.common.core.utils.SpringUtils
import com.ruoyi.common.redis.service.RedisService
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO
import com.wnkx.order.config.OrderVideoProperties
import com.wnkx.order.mapper.OrderVideoMatchPreselectModelMapper
import com.wnkx.order.remote.RemoteService
import com.wnkx.order.service.*
import com.wnkx.order.service.core.OrderResourceService
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Shared
import spock.lang.Specification

class OrderVideoMatchPreselectModelServiceImplTest extends Specification {

    // Mock dependencies
    RemoteService remoteService = Mock()
    IOrderVideoModelService orderVideoModelService = Mock()
    OrderVideoProperties orderVideoProperties = Mock()
    IOrderService orderService = Mock()
    RedisService redisService = Mock()
    OrderResourceService orderResourceService = Mock()
    IOrderVideoContentService videoContentService = Mock()
    OrderVideoMatchPreselectModelMapper baseMapper = Mock()

    // Service under test
    OrderVideoMatchPreselectModelServiceImpl service = new OrderVideoMatchPreselectModelServiceImpl(
            remoteService,
            orderVideoModelService,
            orderVideoProperties,
            orderService,
            redisService,
            orderResourceService,
            videoContentService
    )

    @Shared
    MockedStatic springUtils

    def setupSpec() {
        springUtils = Mockito.mockStatic(SpringUtils.class)
    }

    def cleanupSpec() {
        springUtils.close()
    }

    def setup() {
        // 设置 baseMapper
        service.baseMapper = baseMapper
    }

    def "cleanupInvalidPreselectModels - 正常情况下清理无效预选模特"() {
        given: "准备测试数据"
        def activePreselectModels = createActivePreselectModels()
        def invalidModels = createInvalidModels()
        def overdueModels = createOverdueModels()
        def expiredModels = createExpiredModels()

        // Mock baseMapper 查询活跃预选模特
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activePreselectModels

        // Mock 远程服务调用
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        remoteService.getOverdueModels() >> overdueModels
        remoteService.getExpiredModels() >> expiredModels

        // Mock SpringUtils
        def mockOrderVideoMatchService = Mock(OrderVideoMatchService)
        Mockito.when(SpringUtils.getBean(OrderVideoMatchService.class)).thenReturn(mockOrderVideoMatchService)

        when: "执行清理方法"
        def result = service.cleanupInvalidPreselectModels()

        then: "验证结果"
        result == 6 // 应该清理6条记录
        
        // 验证批量更新被调用
        1 * baseMapper.updateBatchById(_) >> { args ->
            def models = args[0] as List<OrderVideoMatchPreselectModel>
            assert models.size() == 6
            assert models.every { it.status == PreselectStatusEnum.OUT.code }
            assert models.every { it.oustTime != null }
            return true
        }

        // 验证清理匹配单标记被调用
        1 * mockOrderVideoMatchService.clearFlag(_, 1)
    }

    def "cleanupInvalidPreselectModels - 无需清理数据的情况"() {
        given: "准备空数据"
        def activePreselectModels = []

        // Mock baseMapper 返回空列表
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activePreselectModels

        // Mock 远程服务返回空列表
        remoteService.getModelsForPreselectCleanup() >> []
        remoteService.getOverdueModels() >> []
        remoteService.getExpiredModels() >> []

        when: "执行清理方法"
        def result = service.cleanupInvalidPreselectModels()

        then: "验证结果"
        result == 0 // 没有清理任何记录
        
        // 验证没有调用批量更新
        0 * baseMapper.updateBatchById(_)
    }

    def "cleanupInvalidPreselectModels - 远程服务异常情况"() {
        given: "准备测试数据"
        def activePreselectModels = createActivePreselectModels()

        // Mock baseMapper 查询活跃预选模特
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activePreselectModels

        // Mock 远程服务抛出异常
        remoteService.getModelsForPreselectCleanup() >> { throw new RuntimeException("远程服务异常") }
        remoteService.getOverdueModels() >> []
        remoteService.getExpiredModels() >> []

        // Mock SpringUtils
        def mockOrderVideoMatchService = Mock(OrderVideoMatchService)
        Mockito.when(SpringUtils.getBean(OrderVideoMatchService.class)).thenReturn(mockOrderVideoMatchService)

        when: "执行清理方法"
        def result = service.cleanupInvalidPreselectModels()

        then: "验证异常被处理，返回部分清理结果"
        result >= 0 // 应该正常返回，不抛出异常
        
        // 验证日志记录异常但不影响其他清理步骤
        notThrown(Exception)
    }

    def "cleanupInvalidPreselectModels - 边界条件测试"() {
        given: "准备边界条件数据"
        def activePreselectModels = createActivePreselectModels()
        def invalidModels = createInvalidModels()

        // Mock baseMapper
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activePreselectModels

        // Mock 远程服务 - 只有状态异常的模特
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        remoteService.getOverdueModels() >> []
        remoteService.getExpiredModels() >> []

        // Mock SpringUtils
        def mockOrderVideoMatchService = Mock(OrderVideoMatchService)
        Mockito.when(SpringUtils.getBean(OrderVideoMatchService.class)).thenReturn(mockOrderVideoMatchService)

        when: "执行清理方法"
        def result = service.cleanupInvalidPreselectModels()

        then: "验证只清理状态异常的模特"
        result == 2 // 只清理状态异常的2条记录

        1 * baseMapper.updateBatchById(_) >> { args ->
            def models = args[0] as List<OrderVideoMatchPreselectModel>
            assert models.size() == 2
            assert models.every { it.status == PreselectStatusEnum.OUT.code }
            assert models.every { it.oustType == PreselectModelOustTypeEnum.CUSTOMER_SERVICE_ELIMINATION.code }
            return true
        }
    }

    def "cleanupInvalidPreselectModels - 验证清理统计信息"() {
        given: "准备完整测试数据"
        def activePreselectModels = createActivePreselectModels()
        def invalidModels = createInvalidModels()
        def overdueModels = createOverdueModels()
        def expiredModels = createExpiredModels()

        // Mock baseMapper
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activePreselectModels

        // Mock 远程服务
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        remoteService.getOverdueModels() >> overdueModels
        remoteService.getExpiredModels() >> expiredModels

        // Mock SpringUtils
        def mockOrderVideoMatchService = Mock(OrderVideoMatchService)
        Mockito.when(SpringUtils.getBean(OrderVideoMatchService.class)).thenReturn(mockOrderVideoMatchService)

        when: "执行清理方法"
        def result = service.cleanupInvalidPreselectModels()

        then: "验证清理统计和分类"
        result == 6

        1 * baseMapper.updateBatchById(_) >> { args ->
            def models = args[0] as List<OrderVideoMatchPreselectModel>

            // 验证不同类型的清理原因
            def statusInvalidCount = models.count { it.oustType == PreselectModelOustTypeEnum.CUSTOMER_SERVICE_ELIMINATION.code }
            def overdueCount = models.count { it.remark?.contains("逾期") }
            def expiredCount = models.count { it.remark?.contains("过期") }

            assert statusInvalidCount == 2
            assert overdueCount == 2
            assert expiredCount == 2

            return true
        }

        1 * mockOrderVideoMatchService.clearFlag(_, 1)
    }

    def "cleanupInvalidPreselectModels - 测试重复模特ID去重"() {
        given: "准备包含重复模特ID的数据"
        def activePreselectModels = [
                createPreselectModel(1L, 1L, 101L, PreselectStatusEnum.UN_JOINTED.code),
                createPreselectModel(2L, 2L, 101L, PreselectStatusEnum.JOINTED.code), // 重复模特ID
                createPreselectModel(3L, 3L, 102L, PreselectStatusEnum.UN_JOINTED.code)
        ]

        def invalidModels = [createModelInfoVO(101L, "模特101")]
        def overdueModels = [101L] // 同一个模特既状态异常又逾期

        // Mock baseMapper
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activePreselectModels

        // Mock 远程服务
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        remoteService.getOverdueModels() >> overdueModels
        remoteService.getExpiredModels() >> []

        // Mock SpringUtils
        def mockOrderVideoMatchService = Mock(OrderVideoMatchService)
        Mockito.when(SpringUtils.getBean(OrderVideoMatchService.class)).thenReturn(mockOrderVideoMatchService)

        when: "执行清理方法"
        def result = service.cleanupInvalidPreselectModels()

        then: "验证重复模特被正确处理"
        result == 2 // 两条记录都应该被清理

        1 * baseMapper.updateBatchById(_) >> { args ->
            def models = args[0] as List<OrderVideoMatchPreselectModel>
            assert models.size() == 2
            assert models.every { it.modelId == 101L } // 都是同一个模特
            assert models.every { it.status == PreselectStatusEnum.OUT.code }
            return true
        }
    }

    // 辅助方法：创建活跃预选模特数据
    private List<OrderVideoMatchPreselectModel> createActivePreselectModels() {
        return [
                createPreselectModel(1L, 1L, 101L, PreselectStatusEnum.UN_JOINTED.code),
                createPreselectModel(2L, 1L, 102L, PreselectStatusEnum.JOINTED.code),
                createPreselectModel(3L, 2L, 103L, PreselectStatusEnum.UN_JOINTED.code),
                createPreselectModel(4L, 2L, 104L, PreselectStatusEnum.JOINTED.code),
                createPreselectModel(5L, 3L, 105L, PreselectStatusEnum.UN_JOINTED.code),
                createPreselectModel(6L, 3L, 106L, PreselectStatusEnum.JOINTED.code)
        ]
    }

    // 辅助方法：创建无效模特数据
    private List<ModelInfoVO> createInvalidModels() {
        return [
                createModelInfoVO(101L, "模特101"),
                createModelInfoVO(102L, "模特102")
        ]
    }

    // 辅助方法：创建逾期模特数据
    private List<Long> createOverdueModels() {
        return [103L, 104L]
    }

    // 辅助方法：创建过期模特数据
    private List<Long> createExpiredModels() {
        return [105L, 106L]
    }

    // 辅助方法：创建预选模特对象
    private OrderVideoMatchPreselectModel createPreselectModel(Long id, Long matchId, Long modelId, Integer status) {
        def model = new OrderVideoMatchPreselectModel()
        model.id = id
        model.matchId = matchId
        model.modelId = modelId
        model.status = status
        model.addType = PreselectModelAddTypeEnum.OPERATION.code
        model.createTime = DateUtil.date()
        return model
    }

    def "cleanupInvalidPreselectModels - 数据库更新异常测试"() {
        given: "准备测试数据"
        def activePreselectModels = createActivePreselectModels()
        def invalidModels = createInvalidModels()

        // Mock baseMapper 查询成功，但更新失败
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activePreselectModels
        baseMapper.updateBatchById(_) >> { throw new RuntimeException("数据库更新失败") }

        // Mock 远程服务
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        remoteService.getOverdueModels() >> []
        remoteService.getExpiredModels() >> []

        when: "执行清理方法"
        service.cleanupInvalidPreselectModels()

        then: "验证异常被抛出"
        thrown(RuntimeException)
    }

    def "cleanupInvalidPreselectModels - 验证事务注解"() {
        expect: "验证方法有事务注解"
        def method = OrderVideoMatchPreselectModelServiceImpl.class.getMethod("cleanupInvalidPreselectModels")
        def transactionalAnnotation = method.getAnnotation(org.springframework.transaction.annotation.Transactional.class)

        transactionalAnnotation != null
        transactionalAnnotation.rollbackFor() == [Exception.class]
    }

    def "cleanupInvalidPreselectModels - 空匹配单ID列表测试"() {
        given: "准备没有匹配单ID的数据"
        def activePreselectModels = [
                createPreselectModelWithoutMatchId(1L, 101L, PreselectStatusEnum.UN_JOINTED.code)
        ]
        def invalidModels = [createModelInfoVO(101L, "模特101")]

        // Mock baseMapper
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activePreselectModels

        // Mock 远程服务
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        remoteService.getOverdueModels() >> []
        remoteService.getExpiredModels() >> []

        // Mock SpringUtils
        def mockOrderVideoMatchService = Mock(OrderVideoMatchService)
        Mockito.when(SpringUtils.getBean(OrderVideoMatchService.class)).thenReturn(mockOrderVideoMatchService)

        when: "执行清理方法"
        def result = service.cleanupInvalidPreselectModels()

        then: "验证处理正常，但不调用clearFlag"
        result == 1

        1 * baseMapper.updateBatchById(_)
        0 * mockOrderVideoMatchService.clearFlag(_, _) // 因为没有有效的匹配单ID
    }

    def "cleanupInvalidPreselectModels - 验证日志输出"() {
        given: "准备测试数据"
        def activePreselectModels = createActivePreselectModels()
        def invalidModels = createInvalidModels()

        // Mock baseMapper
        baseMapper.selectList(_ as LambdaQueryWrapper) >> activePreselectModels

        // Mock 远程服务
        remoteService.getModelsForPreselectCleanup() >> invalidModels
        remoteService.getOverdueModels() >> []
        remoteService.getExpiredModels() >> []

        // Mock SpringUtils
        def mockOrderVideoMatchService = Mock(OrderVideoMatchService)
        Mockito.when(SpringUtils.getBean(OrderVideoMatchService.class)).thenReturn(mockOrderVideoMatchService)

        when: "执行清理方法"
        def result = service.cleanupInvalidPreselectModels()

        then: "验证方法正常执行"
        result == 2
        noExceptionThrown()
    }

    // 辅助方法：创建模特信息VO
    private ModelInfoVO createModelInfoVO(Long id, String name) {
        def model = new ModelInfoVO()
        model.id = id
        model.name = name
        return model
    }

    // 辅助方法：创建没有匹配单ID的预选模特对象
    private OrderVideoMatchPreselectModel createPreselectModelWithoutMatchId(Long id, Long modelId, Integer status) {
        def model = new OrderVideoMatchPreselectModel()
        model.id = id
        model.matchId = null // 没有匹配单ID
        model.modelId = modelId
        model.status = status
        model.addType = PreselectModelAddTypeEnum.OPERATION.code
        model.createTime = DateUtil.date()
        return model
    }
}
